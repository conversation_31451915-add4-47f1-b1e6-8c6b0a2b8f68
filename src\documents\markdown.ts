import type { Disposable, Event, TextDocumentContentProvider } from 'vscode';
import { EventEmitter, Uri, workspace } from 'vscode';
import { Schemes } from '../constants';
import type { GlCommands } from '../constants.commands';
import { decodeGitLensRevisionUriAuthority, encodeGitLensRevisionUriAuthority } from '../git/gitUri.authority';

// gitlens-markdown:{explain}/{entity}/{entityID}/{model}[{/friendlyName}].md

export interface MarkdownContentMetadata {
	header: {
		title: string;
		subtitle?: string;
		aiModel?: string;
	};
	command?: {
		label: string;
		name: GlCommands;
		args?: Record<string, unknown>;
	};
}

export class MarkdownContentProvider implements TextDocumentContentProvider {
	private contents = new Map<string, string>();
	private registration: Disposable;

	private _onDidChange = new EventEmitter<Uri>();
	get onDidChange(): Event<Uri> {
		return this._onDidChange.event;
	}

	constructor() {
		this.registration = workspace.registerTextDocumentContentProvider(Schemes.GitLensMarkdown, this);

		workspace.onDidCloseTextDocument(document => {
			if (document.uri.scheme === Schemes.GitLensMarkdown) {
				this.contents.delete(document.uri.toString());
			}
		});
	}

	provideTextDocumentContent(uri: Uri): string | undefined {
		let contents = this.contents.get(uri.toString());
		if (contents != null) return contents;

		contents = getContentFromMarkdownUri(uri);
		if (contents != null) return contents;

		return `# ${uri.path}\n\nNo content available.`;
	}

	openDocument(content: string, path: string, label: string, metadata?: MarkdownContentMetadata): Uri {
		const uri = Uri.from({
			scheme: Schemes.GitLensMarkdown,
			authority: metadata ? encodeGitLensRevisionUriAuthority(metadata) : undefined,
			path: `${path}.md`,
			query: JSON.stringify({ label: label }),
		});

		const uriString = uri.toString();
		const existingContent = this.contents.get(uriString);
		const contentChanged = existingContent !== content;

		this.contents.set(uriString, content);

		// If this document already exists and the content changed, fire the change event
		// This will automatically refresh any open previews
		if (contentChanged) {
			this._onDidChange.fire(uri);
		}

		return uri;
	}

	updateDocument(uri: Uri, content: string): void {
		this.contents.set(uri.toString(), content);
		this._onDidChange.fire(uri);
	}

	closeDocument(uri: Uri): void {
		this.contents.delete(uri.toString());
	}

	dispose(): void {
		this.contents.clear();
		this.registration.dispose();
	}
}

function getContentFromMarkdownUri(uri: Uri): string | undefined {
	if (!uri.path.startsWith('/explain')) return undefined;

	const authority = uri.authority;
	if (authority == null || authority.length === 0) return undefined;

	const metadata = decodeGitLensRevisionUriAuthority<MarkdownContentMetadata>(authority);

	if (metadata.header == null) return undefined;

	const headerContent = getMarkdownHeaderContent(metadata);

	if (metadata.command == null) return `${headerContent}\n\nNo content available.`;

	const commandContent = `\n\n\n\n${metadata.command.label} using the \`Regenerate\` editor action in the editor toolbar.`;

	return `${headerContent}\n\n${commandContent}`;
}

export function getMarkdownHeaderContent(metadata: MarkdownContentMetadata): string {
	let headerContent = `# ${metadata.header.title}`;
	if (metadata.header.aiModel != null) {
		headerContent += `\n\n> Generated by ${metadata.header.aiModel}`;
	}
	if (metadata.header.subtitle != null) {
		headerContent += `\n\n## ${metadata.header.subtitle}`;
	}

	return headerContent;
}
